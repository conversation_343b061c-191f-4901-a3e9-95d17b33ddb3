<script lang="ts">
	import type { TwoStepCheckOutSchema } from '$lib/schema';
    import { Button, Input, H3, P2 } from '$lib/ui';
	import { superForm, type Infer, type SuperValidated } from 'sveltekit-superforms';

    let { data } = $props();
    let { form, errors, message, constraints, enhance } = superForm(data.form as SuperValidated<Infer<TwoStepCheckOutSchema>>);

    /** State to track current active step
     * 0: Personal Info 
     * 1: Payment Info
     * 2: Complete
     */
    let currentStep: 0 | 1 | 2 = $state(0);

    $effect(() => {
        if ($message === 'Step 1 completed') currentStep = 1;
        if ($message === 'Step 2 completed') currentStep = 2;
    })
</script>

<div class="step-header">
    <div
        class="step-button"
        class:active={currentStep === 0}
    >
        <div class="step-number">1</div>
        <div class="step-content">
            <H3>Thông tin Khách Hàng</H3>
            <P2>Nhập thông tin cá nhân của bạn</P2>
        </div>
    </div>

    <div
        class="step-button"
        class:active={currentStep === 1}
    >
        <div class="step-number">2</div>
        <div class="step-content">
            <H3>Thông tin Thanh Toán</H3>
            <P2>Hoàn tất thông tin thanh toán</P2>
        </div>
    </div>
</div>

<!-- Order Summary Section -->
<div class="order-summary">
    <div class="order-item">
        <div class="item-details">
            <div class="item-info">
                <div class="item-title">DSAT16 Secret Webinar Bundle</div>
            </div>
        </div>
        <div class="item-price">5.160.000đ</div>
    </div>
</div>

<form method="POST" action="?/step{currentStep + 1}" class="form-container" use:enhance>
    <!-- Step 1 Content -->
    <div class="step-content-form" style:display={currentStep === 0 ? 'flex' : 'none'}>
        <Input fullWidth placeholder="Họ và tên" label="Họ và tên" name="name" bind:value={$form.name} error={$errors.name?.[0]} constraints={$constraints.name} />
        <Input fullWidth placeholder="Email" label="Email" type="email" name="email" bind:value={$form.email} error={$errors.email?.[0]} constraints={$constraints.email} />
        <Input fullWidth placeholder="Số điện thoại" label="Số điện thoại" name="phone" bind:value={$form.phone} error={$errors.phone?.[0]} constraints={$constraints.phone} />
        <Button fullWidth type="submit">Tiếp tục</Button>
    </div>
    
    <!-- Step 2 Content -->
    <div class="step-content-form" style:display={currentStep === 1 ? 'flex' : 'none'}>
        <Input fullWidth placeholder="Số thẻ" label="Số thẻ tín dụng" name="card_number" bind:value={$form.card_number} error={$errors.card_number?.[0]} constraints={$constraints.card_number} />
        <div class="inline-inputs">
            <Input fullWidth placeholder="MM/YY" label="Ngày hết hạn" name="expiration_date" bind:value={$form.expiration_date} error={$errors.expiration_date?.[0]} constraints={$constraints.expiration_date} />
            <Input fullWidth placeholder="CVV" label="Mã CVV" name="cvv" bind:value={$form.cvv} error={$errors.cvv?.[0]} constraints={$constraints.cvv} />
        </div>
        <Input fullWidth placeholder="Tên trên thẻ" label="Tên trên thẻ" name="cardholder_name" bind:value={$form.cardholder_name} error={$errors.cardholder_name?.[0]} constraints={$constraints.cardholder_name} />
        <Button fullWidth type="submit">Hoàn tất đặt hàng</Button>
    </div>
</form>

<style>
    .order-summary {
        background: var(--white);
        border-bottom: 3px solid var(--pitch-black);
        padding: 1.5rem 2rem;
    }

    .order-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        gap: 1rem;
    }

    .item-details {
        display: flex;
        align-items: center;
        gap: 1rem;
        flex: 1;
    }

    .item-info {
        flex: 1;
    }

    .item-title {
        font-family: "Open Sans";
        font-weight: 600;
        font-size: 1rem;
        color: var(--pitch-black);
        line-height: 1.4;
    }

    .item-price {
        font-family: "Open Sans";
        font-weight: 700;
        font-size: 1.25rem;
        color: var(--pitch-black);
        flex-shrink: 0;
    }

    .step-header {
        display: flex;
        gap: 0;
        overflow: hidden;
        border-radius: 0.75rem 0.75rem 0 0;
        border-bottom: 3px solid var(--pitch-black);
    }

    .step-button {
        flex: 1;
        display: flex;
        align-items: center;
        gap: 1rem;
        padding: 1.5rem;
        background: var(--white);
        border: none;
        transition: all 0.2s ease;
        position: relative;
    }

    .step-button:first-child {
        border-right: 3px solid var(--pitch-black);
    }

    .step-button.active {
        background: var(--aquamarine);
    }

    .step-button:not(.active) {
        background: #f5f5f5;
        color: #666;
    }

    .step-number {
        width: 3rem;
        height: 3rem;
        border-radius: 50%;
        background: var(--pitch-black);
        color: var(--white);
        display: flex;
        align-items: center;
        justify-content: center;
        font-family: "Open Sans";
        font-weight: 700;
        font-size: 1.25rem;
        flex-shrink: 0;
    }

    .step-button.active .step-number {
        background: var(--pitch-black);
        color: var(--white);
    }

    .step-button:not(.active) .step-number {
        background: #ccc;
        color: var(--white);
    }

    .step-content {
        text-align: left;
        flex: 1;
    }

    .form-container {
        padding: 2rem;
    }

    .step-content-form {
        display: flex;
        flex-direction: column;
        gap: 1rem;
    }

    .inline-inputs {
        display: flex;
        gap: 1rem;
        min-width: 0;
    }

    @media (max-width: 768px) {
        .order-summary {
            padding: 1rem;
        }

        .order-item {
            gap: 0.75rem;
            text-wrap: balance;
        }

        .item-details {
            width: 100%;
        }

        .item-title {
            font-size: 0.9rem;
        }

        .item-price {
            font-size: 1.125rem;
            align-self: flex-end;
        }

        .step-button {
            flex-direction: column;
            text-align: center;
            padding: 1rem;
            gap: 0.5rem;
        }

        .step-content {
            text-align: center;
        }

        .inline-inputs {
            flex-direction: column;
        }
    }
</style>

